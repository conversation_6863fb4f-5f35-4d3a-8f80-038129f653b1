# Remote Claude-Code Controller 🤖📱

A Python wrapper that enables seamless remote control of Claude-Code CLI via Telegram, allowing you to switch between local terminal and mobile control without missing context.

## Features ✨

- **Dual Input Handling**: Control from both local terminal AND Telegram simultaneously
- **Interactive Menu Detection**: Automatically converts numbered CLI menus into Telegram buttons
- **Real-time Streaming**: All Claude output streams to both local terminal and Telegram
- **Smart Button Management**: Buttons auto-clear after selection with confirmation
- **Graceful Fallback**: Works in local-only mode if Telegram isn't configured
- **Session Logging**: Maintains rolling log buffer (500 lines) in `remocode.log`
- **Race Condition Handling**: First response (local OR remote) wins

## Quick Start 🚀

### 1. Setup Environment

```bash
# Run the setup script
chmod +x setup.sh
./setup.sh
```

### 2. Configure Telegram (Optional)

1. Create a Telegram bot via [@BotFather](https://t.me/botfather)
2. Get your chat ID (send a message to your bot, then visit: `https://api.telegram.org/bot<TOKEN>/getUpdates`)
3. Edit `.env` file:

```env
TG_TOKEN=your_bot_token_here
TG_CHAT=your_chat_id_here
```

### 3. Run

```bash
# Activate virtual environment
source .venv/bin/activate

# Start remote session
./remocode.py

# Or with a spec file
./remocode.py path/to/spec.md
```

## How It Works 🔧

### Menu Detection
The script uses regex pattern `r"\s*(\d+)\)\s*(.+?)"` to detect numbered menus in Claude's output and automatically creates Telegram inline keyboard buttons.

### Dual Control
- **Local Terminal**: Always shows real-time output, accepts direct keyboard input
- **Telegram Interface**: Receives silent stream of all output, interactive buttons for choices only
- **Choice Selection**: First response (local OR remote) wins, other interface shows confirmation

### Button Lifecycle
1. Menu detected → Telegram buttons appear
2. User selects (local keyboard OR Telegram button)
3. Choice sent to Claude CLI
4. Buttons replaced with "✅ chosen X" confirmation

## Architecture 🏗️

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Local Term    │    │   remocode.py    │    │    Telegram     │
│                 │◄──►│                  │◄──►│                 │
│ • Real-time out │    │ • pexpect spawn  │    │ • Silent stream │
│ • Direct input  │    │ • Menu detection │    │ • Button menus  │
│ • Always works  │    │ • Dual handling  │    │ • Mobile ready  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌──────────────────┐
                    │  Claude-Code CLI │
                    │                  │
                    │ • Pseudo-terminal│
                    │ • Full context   │
                    │ • All features   │
                    └──────────────────┘
```

## Configuration Options ⚙️

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `TG_TOKEN` | Telegram bot token | None (local-only mode) |
| `TG_CHAT` | Telegram chat/topic ID | None |
| `CLAUDE_CMD` | Claude-Code command | `npx @anthropic-ai/claude-code` |

### Command Line Usage

```bash
# Interactive session
./remocode.py

# With spec file
./remocode.py path/to/specification.md

# With custom Claude command
CLAUDE_CMD="claude --model sonnet" ./remocode.py
```

## Advanced Usage 🎯

### Multiple Instances
The design supports multiple concurrent Claude-Code instances via GitHub Actions or separate terminals:

```bash
# Terminal 1
TG_CHAT=chat_id_1 ./remocode.py spec1.md

# Terminal 2  
TG_CHAT=chat_id_2 ./remocode.py spec2.md
```

### Integration with Gemini Review
Hook point for post-execution review:

```python
# Add to end of main() function
if spec_file and claude_process.exitstatus == 0:
    await run_gemini_review(spec_file)
```

### GitHub Actions Integration
```yaml
- name: Run Remote Claude-Code
  run: |
    source .venv/bin/activate
    ./remocode.py ${{ github.workspace }}/spec.md
  env:
    TG_TOKEN: ${{ secrets.TG_TOKEN }}
    TG_CHAT: ${{ secrets.TG_CHAT }}
```

## Troubleshooting 🔧

### Common Issues

1. **"No module named 'pexpect'"**
   ```bash
   source .venv/bin/activate
   uv pip install -r requirements.txt
   ```

2. **Telegram messages not sending**
   - Check TG_TOKEN and TG_CHAT in `.env`
   - Verify bot has permission to send messages
   - Test with: `curl -X GET "https://api.telegram.org/bot<TOKEN>/getMe"`

3. **Claude process not starting**
   - Verify Claude-Code CLI is installed: `npx @anthropic-ai/claude-code --help`
   - Check CLAUDE_CMD in `.env`
   - Ensure Claude subscription is active

4. **Menu detection not working**
   - Check log output in `remocode.log`
   - Verify menu format matches regex pattern
   - Test with `--debug` flag

### Debug Mode

```bash
# Enable debug logging
python -c "import logging; logging.basicConfig(level=logging.DEBUG)"
./remocode.py --debug
```

## Limitations & Future Enhancements 📋

### Current Limitations
- Telegram message limit: 4096 characters
- Menu detection requires specific numbered format
- No file upload/download via Telegram (yet)

### Planned Enhancements
- File transfer support
- Voice message input
- Multi-user session sharing
- Web interface option
- Docker containerization

## Contributing 🤝

1. Fork the repository
2. Create feature branch: `git checkout -b feature/amazing-feature`
3. Commit changes: `git commit -m 'Add amazing feature'`
4. Push to branch: `git push origin feature/amazing-feature`
5. Open Pull Request

## License 📄

MIT License - see LICENSE file for details.

---

**Built with ❤️ for seamless remote development**
