#!/usr/bin/env python3
"""
Test script for menu detection functionality
"""

import re
from typing import List, Dict, Any

def detect_menu(text: str) -> List[Dict[str, Any]]:
    """Detect numbered menu options in text"""
    menu_pattern = r'^\s*(\d+)\)\s*(.+?)$'
    matches = re.findall(menu_pattern, text, re.MULTILINE)

    if len(matches) < 2:  # Need at least 2 options to be a menu
        return []

    return [{"number": int(num), "text": desc.strip()} for num, desc in matches if desc.strip()]

# Test cases
test_cases = [
    """
Choose an option:
1) Create a new file
2) Edit existing file
3) Delete file
4) Exit
""",
    """
What would you like to do?
1) Start new conversation
2) Continue previous conversation
3) View conversation history
""",
    """
This is not a menu.
Just some regular text.
""",
    """
1) Single option only
""",
    """
Select your preference:
  1) Option A with some details
  2) Option B with more information
  3) Option C with even longer description text
""",
]

def main():
    print("🧪 Testing menu detection...")

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i} ---")
        print("Input:")
        print(repr(test_case))

        menu_items = detect_menu(test_case)
        print(f"\nDetected menu items: {len(menu_items)}")

        for item in menu_items:
            print(f"  {item['number']}) {item['text']}")

        if not menu_items:
            print("  (No menu detected)")

if __name__ == "__main__":
    main()
