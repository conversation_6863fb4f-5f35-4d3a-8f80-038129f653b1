{"permissions": {"allow": ["<PERSON><PERSON>(tail:*)", "Bash(rg:*)", "Bash(npm run build:*)", "Bash(npm test:*)", "<PERSON><PERSON>(mv:*)", "Bash(find:*)", "Bash(npm run lint)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "WebFetch(domain:reactflow.dev)", "WebFetch(domain:github.com)", "Bash(npm install)", "Bash(grep:*)", "<PERSON><PERSON>(curl:*)", "mcp__ide__getDiagnostics", "WebFetch(domain:ai-sdk.dev)", "WebFetch(domain:vercel.com)", "WebFetch(domain:ai-sdk-reasoning.vercel.app)", "WebFetch(domain:sdk.vercel.ai)", "WebFetch(domain:www.assistant-ui.com)", "WebFetch(domain:www.assistant-ui.com)", "Bash(gemini:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npx tsc:*)", "Bash(npx eslint:*)", "Bash(ls:*)", "Bash(npx prisma migrate dev:*)"], "deny": []}, "hooks": {"PreToolUse": [{"hooks": [{"type": "command", "command": "python .remocode/hooks/pretool_guard.py"}]}], "PostToolUse": [{"hooks": [{"type": "command", "command": "python .remocode/hooks/clear_tg_prompt.py"}]}]}}