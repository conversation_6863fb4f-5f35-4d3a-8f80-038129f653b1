#!/usr/bin/env python3
"""
Remote Claude-Code Controller via Telegram
A 150-line Python wrapper that enables remote control of Claude-Code CLI via Telegram.
"""

import os
import sys
import re
import select
import signal
import asyncio
import logging
from typing import Optional, List, Dict, Any
from collections import deque
import pexpect
from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup, Update
from telegram.ext import Application, CallbackQueryHandler, MessageHandler, filters
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configuration
TG_TOKEN = os.getenv('TG_TOKEN')
TG_CHAT = os.getenv('TG_CHAT')
CLAUDE_CMD = os.getenv('CLAUDE_CMD', 'npx @anthropic-ai/claude-code')

# Global state
claude_process: Optional[pexpect.spawn] = None
bot: Optional[Bot] = None
log_buffer = deque(maxlen=500)
active_menu_message_id: Optional[int] = None
choice_made = False

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('remocode.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def log_and_buffer(message: str):
    """Log message and add to buffer for Telegram streaming"""
    logger.info(message)
    log_buffer.append(message)

async def send_telegram_message(text: str, reply_markup=None) -> Optional[int]:
    """Send message to Telegram and return message ID"""
    if not bot or not TG_CHAT:
        return None
    try:
        message = await bot.send_message(
            chat_id=TG_CHAT,
            text=text[:4096],  # Telegram message limit
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
        return message.message_id
    except Exception as e:
        logger.error(f"Failed to send Telegram message: {e}")
        return None

async def edit_telegram_message(message_id: int, text: str, reply_markup=None):
    """Edit existing Telegram message"""
    if not bot or not TG_CHAT:
        return
    try:
        await bot.edit_message_text(
            chat_id=TG_CHAT,
            message_id=message_id,
            text=text[:4096],
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )
    except Exception as e:
        logger.error(f"Failed to edit Telegram message: {e}")

def detect_menu(text: str) -> List[Dict[str, Any]]:
    """Detect numbered menu options in text"""
    menu_pattern = r'^\s*(\d+)\)\s*(.+?)$'
    matches = re.findall(menu_pattern, text, re.MULTILINE)

    if len(matches) < 2:  # Need at least 2 options to be a menu
        return []

    return [{"number": int(num), "text": desc.strip()} for num, desc in matches if desc.strip()]

async def create_telegram_keyboard(menu_items: List[Dict[str, Any]]) -> InlineKeyboardMarkup:
    """Create Telegram inline keyboard from menu items"""
    keyboard = []
    for item in menu_items:
        button = InlineKeyboardButton(
            text=f"#{item['number']}: {item['text'][:30]}...",
            callback_data=str(item['number'])
        )
        keyboard.append([button])

    return InlineKeyboardMarkup(keyboard)

async def handle_callback_query(update: Update, context):
    """Handle Telegram button presses"""
    global choice_made, active_menu_message_id

    query = update.callback_query
    await query.answer()

    if choice_made:
        return

    choice = query.data
    choice_made = True

    # Update message to show selection
    await edit_telegram_message(
        active_menu_message_id,
        f"✅ Selected option #{choice}",
        reply_markup=None
    )

    # Send choice to Claude process
    if claude_process and claude_process.isalive():
        claude_process.sendline(choice)
        log_and_buffer(f"Sent choice: {choice}")

async def handle_telegram_message(update: Update, context):
    """Handle regular Telegram messages"""
    if claude_process and claude_process.isalive():
        message = update.message.text
        claude_process.sendline(message)
        log_and_buffer(f"Sent message: {message}")

def setup_telegram_bot():
    """Setup Telegram bot with handlers"""
    global bot

    if not TG_TOKEN:
        logger.warning("No Telegram token provided, running in local-only mode")
        return None

    application = Application.builder().token(TG_TOKEN).build()
    bot = application.bot

    # Add handlers
    application.add_handler(CallbackQueryHandler(handle_callback_query))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_telegram_message))

    return application

async def process_claude_output():
    """Process Claude CLI output and handle menus"""
    global active_menu_message_id, choice_made

    output_buffer = ""

    while claude_process and claude_process.isalive():
        try:
            # Read output with timeout
            ready = claude_process.expect([pexpect.TIMEOUT, pexpect.EOF], timeout=0.1)

            if ready == 0:  # TIMEOUT - data available
                if claude_process.before:
                    chunk = claude_process.before.decode('utf-8', errors='ignore')
                    output_buffer += chunk
                    print(chunk, end='', flush=True)  # Local terminal output

                    # Check for complete lines
                    lines = output_buffer.split('\n')
                    if len(lines) > 1:
                        complete_text = '\n'.join(lines[:-1])
                        output_buffer = lines[-1]

                        # Stream to Telegram (non-blocking)
                        if complete_text.strip() and bot:
                            asyncio.create_task(send_telegram_message(f"```\n{complete_text}\n```"))

                        # Check for menu
                        menu_items = detect_menu(complete_text)
                        if menu_items and not choice_made:
                            keyboard = await create_telegram_keyboard(menu_items)
                            active_menu_message_id = await send_telegram_message(
                                "🔢 Choose an option:",
                                reply_markup=keyboard
                            )

                            # Wait for choice (local or remote)
                            choice_made = False
                            while not choice_made and claude_process.isalive():
                                # Check for local keyboard input (non-blocking)
                                if sys.stdin in select.select([sys.stdin], [], [], 0.1)[0]:
                                    local_choice = sys.stdin.readline().strip()
                                    if local_choice and not choice_made:
                                        choice_made = True
                                        claude_process.sendline(local_choice)
                                        if active_menu_message_id:
                                            await edit_telegram_message(
                                                active_menu_message_id,
                                                f"✅ Selected option #{local_choice} (local)",
                                                reply_markup=None
                                            )
                                        break
                                await asyncio.sleep(0.1)
            else:  # EOF - process ended
                break

        except Exception as e:
            logger.error(f"Error processing output: {e}")
            break

async def main():
    """Main function"""
    global claude_process

    # Setup signal handlers
    def signal_handler(signum, frame):
        logger.info("Received interrupt signal")
        if claude_process:
            claude_process.terminate()
        sys.exit(0)

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    # Setup Telegram bot
    telegram_app = setup_telegram_bot()

    # Get command line arguments
    spec_file = sys.argv[1] if len(sys.argv) > 1 else None
    claude_args = [CLAUDE_CMD]
    if spec_file:
        claude_args.extend(['-p', spec_file])

    try:
        # Start Claude process
        logger.info(f"Starting Claude process: {' '.join(claude_args)}")
        claude_process = pexpect.spawn(' '.join(claude_args), encoding='utf-8')
        claude_process.logfile_read = sys.stdout

        if telegram_app:
            await send_telegram_message("🚀 Claude-Code remote session started")

        # Start Telegram bot if configured
        if telegram_app:
            await telegram_app.initialize()
            await telegram_app.start()

            # Process Claude output
            await process_claude_output()

            await telegram_app.stop()
        else:
            # Local-only mode
            claude_process.interact()

    except Exception as e:
        logger.error(f"Error: {e}")
        if telegram_app:
            await send_telegram_message(f"❌ Error: {e}")
    finally:
        if claude_process:
            claude_process.terminate()
        logger.info("Session ended")

if __name__ == "__main__":
    asyncio.run(main())
