#!/usr/bin/env python3
"""
Autonomous Romocode System - Enhanced Pretool Guard

This system enables Claude Code to operate autonomously while maintaining safety through:
1. **Default Allow**: Most operations are auto-approved for seamless workflow
2. **Smart Blocklist**: Only dangerous operations require approval (git push, database writes, etc.)
3. **Persistent Permissions**: User preferences are remembered across sessions
4. **Context Awareness**: Understands planning vs execution phases
5. **Mirror Interface**: Shows exact Claude Code options in Telegram
6. **Continuation Support**: Enables follow-up instructions without interruption

Key improvements:
- Expanded auto-approval for file operations, codebase exploration, task management
- Persistent permission storage beyond 2-hour sessions
- Configurable blocklist for critical operations
- Enhanced user experience with minimal interruptions
"""

from __future__ import annotations

import json
import os
import re
import sys
import time
from pathlib import Path
from typing import Dict, List, Tuple, Set

import requests

# ---------------------------------------------------------------------------
# Configuration
# ---------------------------------------------------------------------------
SESSION_FLAG_FILE = Path.home() / ".cache" / "claude_auto_approve_session"
PERSISTENT_PERMISSIONS_FILE = Path.home() / ".cache" / "claude_persistent_permissions.json"
SESSION_TTL_SECS = 7200  # 2 hours
PERSISTENT_TTL_SECS = 30 * 24 * 3600  # 30 days

# ---------------------------------------------------------------------------
# Permission Categories & Blocklist
# ---------------------------------------------------------------------------

# Operations that are ALWAYS auto-approved (autonomous operation)
AUTONOMOUS_TOOLS = {
    # File operations (reading, writing, editing)
    "Read", "Write", "Edit", "MultiEdit", "str-replace-editor", "save-file", "remove-files",

    # Codebase exploration
    "codebase-retrieval", "git-commit-retrieval", "grep_search", "codebase_search",
    "file_search", "list_dir", "Glob", "view", "view-range-untruncated", "search-untruncated",

    # Task management
    "view_tasklist", "add_tasks", "update_tasks", "reorganize_tasklist",

    # Development tools
    "diagnostics", "render-mermaid", "remember",

    # Safe web operations
    "web-search", "web-fetch",

    # Process management (reading only)
    "read-terminal", "read-process", "list-processes",
}

# Bash commands that are auto-approved (safe development operations)
AUTONOMOUS_BASH_PATTERNS = [
    r"^npm run test.*",
    r"^npm run build.*",
    r"^npm run lint.*",
    r"^npm run typecheck.*",
    r"^npm install.*",
    r"^npx tsc.*",
    r"^npx eslint.*",
    r"^npx prisma migrate dev.*",
    r"^ls.*", r"^find.*", r"^grep.*", r"^rg.*", r"^tail.*",
    r"^cat.*", r"^head.*", r"^wc.*", r"^sort.*",
    r"^mkdir.*", r"^mv.*", r"^cp.*", r"^rm (?!-rf).*",  # Allow rm but not rm -rf
    r"^git status.*", r"^git diff.*", r"^git log.*", r"^git show.*",
    r"^curl.*", r"^wget.*",
    r"^gemini.*",
]

# Operations that ALWAYS require approval (critical/dangerous)
BLOCKED_OPERATIONS = {
    # Git operations that affect remote
    "git_push", "git_merge", "git_rebase",

    # Database operations
    "database_write", "database_delete", "prisma_migrate_deploy",

    # System operations
    "system_shutdown", "system_reboot",

    # Deployment
    "deploy", "publish",
}

# Bash commands that ALWAYS require approval
BLOCKED_BASH_PATTERNS = [
    r"^git push.*",
    r"^git merge.*",
    r"^git rebase.*",
    r"^git checkout main.*",
    r"^git checkout master.*",
    r"^rm -rf.*",
    r"^sudo.*",
    r"^chmod 777.*",
    r"^npm publish.*",
    r"^yarn publish.*",
    r"^vercel --prod.*",
    r"^docker.*",
]

# ---------------------------------------------------------------------------
# Helper functions
# ---------------------------------------------------------------------------

def _load_env() -> None:
    """Populate os.environ with key/value pairs from the repo‐local .env file."""
    env_path = Path(__file__).resolve().parents[2] / ".env"
    if not env_path.exists():
        return
    for line in env_path.read_text().splitlines():
        if line.strip() and not line.startswith("#") and "=" in line:
            key, val = line.split("=", 1)
            # Do not overwrite existing environment variables – let callers override.
            os.environ.setdefault(key, val)


def _session_is_active() -> bool:
    """Return True when a previous \"Yes in this session\" is still valid."""
    try:
        ts = float(SESSION_FLAG_FILE.read_text())
        return (time.time() - ts) < SESSION_TTL_SECS
    except FileNotFoundError:
        return False
    except Exception:
        # Corrupted file → ignore
        return False


def _activate_session():
    SESSION_FLAG_FILE.parent.mkdir(parents=True, exist_ok=True)
    SESSION_FLAG_FILE.write_text(str(time.time()))


def _load_persistent_permissions() -> Dict[str, float]:
    """Load persistent permissions from file."""
    try:
        if PERSISTENT_PERMISSIONS_FILE.exists():
            data = json.loads(PERSISTENT_PERMISSIONS_FILE.read_text())
            # Clean expired permissions
            current_time = time.time()
            return {k: v for k, v in data.items() if (current_time - v) < PERSISTENT_TTL_SECS}
        return {}
    except Exception:
        return {}


def _save_persistent_permissions(permissions: Dict[str, float]):
    """Save persistent permissions to file."""
    try:
        PERSISTENT_PERMISSIONS_FILE.parent.mkdir(parents=True, exist_ok=True)
        PERSISTENT_PERMISSIONS_FILE.write_text(json.dumps(permissions))
    except Exception:
        pass


def _has_persistent_permission(tool: str, command: str = "") -> bool:
    """Check if user has granted persistent permission for this operation."""
    permissions = _load_persistent_permissions()

    # Check for tool-level permission
    if tool in permissions:
        return True

    # Check for command-specific permission (for Bash)
    if tool == "Bash" and command:
        cmd_key = f"Bash:{command}"
        if cmd_key in permissions:
            return True

        # Check for pattern-based permissions
        for perm_key in permissions:
            if perm_key.startswith("Bash:") and _matches_pattern(command, perm_key[5:]):
                return True

    return False


def _grant_persistent_permission(tool: str, command: str = "", duration_days: int = 30):
    """Grant persistent permission for an operation."""
    permissions = _load_persistent_permissions()
    current_time = time.time()

    if tool == "Bash" and command:
        key = f"Bash:{command}"
    else:
        key = tool

    permissions[key] = current_time
    _save_persistent_permissions(permissions)


def _matches_pattern(command: str, pattern: str) -> bool:
    """Check if command matches a pattern (supports wildcards)."""
    try:
        regex_pattern = pattern.replace("*", ".*").replace("?", ".")
        return bool(re.match(f"^{regex_pattern}$", command))
    except Exception:
        return False


def _is_claude_interactive_prompt(message: str) -> bool:
    """Check if this is Claude's interactive prompt that should be shown in Telegram."""
    interactive_indicators = [
        "Would you like to proceed?",
        "Ready to code?",
        "Here is Claude's plan:",
        "❯ 1.",  # Claude's option indicator
        "Choose an option:",
        "What would you like to do?",
        "Select:",
        "Multi-Layer Memory System Implementation",  # Specific to your case
        "Yes, and auto-accept edits",
        "Yes, and manually approve edits",
        "No, keep planning",
    ]

    return any(indicator in message for indicator in interactive_indicators)



def _is_autonomous_operation(tool: str, tool_input: dict, message: str = "") -> bool:
    """Determine if this operation should be auto-approved for autonomous workflow."""

    # IMPORTANT: Never auto-approve exit_plan_mode - this is Claude's planning interface
    if tool == "exit_plan_mode":
        return False

    # IMPORTANT: Never auto-approve Claude's interactive prompts - user needs to see these
    if _is_claude_interactive_prompt(message):
        return False

    # Check if tool is in autonomous list
    if tool in AUTONOMOUS_TOOLS:
        return True

    # Special handling for Bash commands
    if tool == "Bash":
        cmd_str = tool_input.get("command", "")

        # Check against blocked patterns first (safety first)
        for pattern in BLOCKED_BASH_PATTERNS:
            if re.match(pattern, cmd_str):
                return False

        # Check against autonomous patterns
        for pattern in AUTONOMOUS_BASH_PATTERNS:
            if re.match(pattern, cmd_str):
                return True

    # Check for persistent permissions
    cmd_str = tool_input.get("command", "") if tool == "Bash" else ""
    if _has_persistent_permission(tool, cmd_str):
        return True

    return False


def _is_blocked_operation(tool: str, tool_input: dict) -> bool:
    """Check if this operation is explicitly blocked and requires approval."""

    # exit_plan_mode is always blocked - it's Claude's interactive planning interface
    if tool == "exit_plan_mode":
        return True

    if tool in BLOCKED_OPERATIONS:
        return True

    if tool == "Bash":
        cmd_str = tool_input.get("command", "")
        for pattern in BLOCKED_BASH_PATTERNS:
            if re.match(pattern, cmd_str):
                return True

    return False


def _parse_options(message: str) -> Dict[str, str]:
    """Extract numbered options from Claude's message.

    Supports patterns:
      1) Option text
      1. Option text
      ❯ 1. Option text  (leading symbols allowed)
    """
    pattern = re.compile(r"^[^\d\n]*?(\d+)(?:[\)\.]|)\s+(.+)$", flags=re.M)
    return {num: text.strip() for num, text in pattern.findall(message)}


def _build_keyboard(opts: Dict[str, str] | None, cmd: str, tool: str, tool_input: dict, full_message: str = "") -> Tuple[List[List[Dict]], str]:
    """Return (keyboard, telegram_message_text) with enhanced options."""

    # Special handling for exit_plan_mode tool
    if tool == "exit_plan_mode":
        # For planning mode, always show the standard planning options
        keyboard = [
            [{"text": "1. Yes, and auto-accept edits", "callback_data": "1"}],
            [{"text": "2. Yes, and manually approve edits", "callback_data": "2"}],
            [{"text": "3. No, keep planning", "callback_data": "3"}]
        ]



        # Extract plan content from the message - try multiple sources
        plan_content = ""

        # First, try to get from full_message
        if full_message and len(full_message.strip()) > 20:
            plan_content = full_message
        # Then try from cmd if it has substantial content
        elif cmd and len(cmd.strip()) > 20:
            plan_content = cmd
        # Check if there's content in tool_input
        elif tool_input and isinstance(tool_input, dict):
            for key in ['message', 'content', 'plan', 'text']:
                if key in tool_input and tool_input[key]:
                    plan_content = str(tool_input[key])
                    break



        # Parse the plan content to extract meaningful information
        if plan_content and plan_content != "exit_plan_mode" and len(plan_content) > 50:
            lines = plan_content.split('\n')
            plan_lines = []

            # Look for plan indicators and extract relevant content
            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # Skip box drawing characters and extract content
                if any(indicator in line for indicator in [
                    "Here is Claude's plan:",
                    "Implementation Plan",
                    "Phase 1:", "Phase 2:", "Phase 3:", "Phase 4:", "Phase 5:",
                    "Step 1:", "Step 2:", "Step 3:",
                    "Database Schema", "Core Logging", "Memory System",
                    "Key Implementation Details:",
                    "Would you like to proceed?"
                ]):
                    # Clean up the line by removing box drawing characters
                    clean_line = line.replace('│', '').replace('╭', '').replace('╮', '').replace('╰', '').replace('╯', '').replace('─', '').strip()
                    if clean_line and clean_line != "Would you like to proceed?":
                        plan_lines.append(clean_line)

                # Stop at "Would you like to proceed?" or similar
                if "Would you like to proceed?" in line or "❯ 1." in line:
                    break

                # Limit length to prevent message being too long
                if len(plan_lines) > 12:
                    plan_lines.append("... (plan continues)")
                    break

            if plan_lines:
                plan_text = '\n'.join(plan_lines)
                text = f"<b>🤖 Claude Code Planning Session:</b>\n\n<pre>{plan_text}</pre>\n\n<b>Would you like to proceed?</b>"
            else:
                text = f"<b>🤖 Claude Code Planning Session:</b>\n\n<pre>Claude has prepared an implementation plan.\n\n📋 Check your terminal for full plan details.\n\n💡 Your options:\n• Option 1: Auto-accept all edits\n• Option 2: Manually approve each edit\n• Option 3: Provide feedback to refine the plan</pre>\n\n<b>Would you like to proceed?</b>"
        else:
            # When we don't have the plan content, provide a helpful message
            text = f"<b>🤖 Claude Code Planning Session:</b>\n\n<pre>Claude has prepared an implementation plan.\n\n📋 Check your terminal for full plan details.\n\n💡 Your options:\n• Option 1: Auto-accept all edits\n• Option 2: Manually approve each edit\n• Option 3: Provide feedback to refine the plan</pre>\n\n<b>Would you like to proceed?</b>"

        return keyboard, text

    # If Claude provided specific options, use them exactly
    if opts:
        keyboard = [[{"text": f"{n}. {label}", "callback_data": n}] for n, label in opts.items()]

        # For Claude's interactive prompts, show the full context
        if _is_claude_interactive_prompt(full_message):
            # Extract the plan/context from the message
            lines = full_message.split('\n')
            context_lines = []
            in_plan = False

            for line in lines:
                if "Here is Claude's plan:" in line or "Ready to code?" in line:
                    in_plan = True
                    context_lines.append(line)
                elif in_plan and line.strip():
                    context_lines.append(line)
                elif "Would you like to proceed?" in line:
                    context_lines.append(line)
                    break

            # Create a concise but informative message
            if context_lines:
                context = '\n'.join(context_lines[:10])  # Limit to first 10 lines
                if len(context_lines) > 10:
                    context += "\n... (plan continues)"
                text = f"<b>🤖 Claude Code Planning Session:</b>\n\n<pre>{context}</pre>"
            else:
                text = f"<b>🤖 Claude Code asks:</b>\n<pre>{cmd}</pre>"
        else:
            text = f"<b>🤖 Claude Code asks:</b>\n<pre>{cmd}</pre>"

        return keyboard, text

    # Enhanced fallback options for autonomous workflow
    keyboard = [
        [
            {"text": "✅ Yes", "callback_data": "yes"},
            {"text": "🔄 Yes, remember for 30 days", "callback_data": "remember"},
        ],
        [
            {"text": "⏳ Yes in this session", "callback_data": "session"},
            {"text": "💬 Give instruction", "callback_data": "instruct"},
        ],
        [
            {"text": "⛔️ No", "callback_data": "no"},
        ]
    ]

    # Create descriptive text
    operation_desc = _describe_operation(tool, tool_input)
    text = f"<b>🤖 Claude Code requests permission:</b>\n<code>{operation_desc}</code>"

    return keyboard, text


def _describe_operation(tool: str, tool_input: dict) -> str:
    """Create a human-friendly description of the operation."""
    if tool == "WebFetch":
        return f"Fetch {tool_input.get('url', '<unknown url>')}"
    elif tool == "WebSearch":
        return f"Search: {tool_input.get('query', '')}"
    elif tool in {"Read", "Write", "Edit", "MultiEdit", "str-replace-editor", "save-file"}:
        return f"{tool} {tool_input.get('file_path', tool_input.get('path', ''))}"
    elif tool == "Bash":
        return tool_input.get("command", "")
    elif tool == "launch-process":
        return f"Run: {tool_input.get('command', '')}"
    else:
        # For other tools, show the tool name and key parameters
        key_params = []
        for key in ["path", "query", "url", "command"]:
            if key in tool_input:
                key_params.append(f"{key}={tool_input[key]}")

        if key_params:
            return f"{tool}({', '.join(key_params)})"
        else:
            return tool


# ---------------------------------------------------------------------------
# Main Logic
# ---------------------------------------------------------------------------

def main() -> None:  # noqa: C901  (enhanced autonomous workflow)
    _load_env()

    # Read JSON payload provided by Claude Code
    payload = json.load(sys.stdin)
    tool = payload.get("tool_name")
    tool_input = payload.get("tool_input", {})
    msg = payload.get("message", "")



    # AUTONOMOUS WORKFLOW: Auto-approve most operations

    # 1. Check if session-wide approval is active
    if _session_is_active():
        print("yes")
        sys.exit(0)

    # 2. Check if this is an autonomous operation (should be auto-approved)
    if _is_autonomous_operation(tool, tool_input, msg):
        print("yes")
        sys.exit(0)

    # 3. If we reach here, this operation needs approval
    # But first check if Telegram is configured
    TG_TOKEN = os.environ.get("TG_TOKEN")
    TG_UID = os.environ.get("TG_UID")
    if not TG_TOKEN or not TG_UID:
        # No Telegram configured - but still block critical operations like exit_plan_mode
        if tool == "exit_plan_mode" or _is_blocked_operation(tool, tool_input):
            sys.stderr.write("⚠️  Critical operation blocked - TG_TOKEN/TG_UID not set.\n")
            sys.stderr.write("⚠️  Configure Telegram to interact with Claude's planning mode.\n")
            print("no")
            sys.exit(1)
        else:
            # For non-critical operations, fall back to allowing (autonomous mode)
            sys.stderr.write("⚠️  TG_TOKEN/TG_UID not set – running in autonomous mode.\n")
            print("yes")
            sys.exit(0)

    TG_API = f"https://api.telegram.org/bot{TG_TOKEN}"

    # 4. Check if this is a blocked operation (always requires approval)
    is_blocked = _is_blocked_operation(tool, tool_input)

    # 5. For non-blocked operations, provide a gentle notification but still auto-approve
    # This gives user visibility without interrupting workflow
    if not is_blocked:
        # Send notification but don't wait for response
        operation_desc = _describe_operation(tool, tool_input)
        try:
            requests.post(
                f"{TG_API}/sendMessage",
                json={
                    "chat_id": TG_UID,
                    "text": f"🤖 <b>Claude Code is running:</b>\n<code>{operation_desc}</code>",
                    "parse_mode": "HTML",
                    "disable_notification": True,  # Silent notification
                },
                timeout=5,
            )
        except Exception:
            pass  # Don't fail if notification fails

        print("yes")
        sys.exit(0)

    # 6. For operations that need approval, request it with enhanced interface

    cmd = _describe_operation(tool, tool_input)



    # Different feedback based on operation type
    if _is_claude_interactive_prompt(msg):
        sys.stderr.write("🤖 Claude is asking for your input…\n")
    elif tool == "exit_plan_mode":
        sys.stderr.write("🎯 Processing Claude planning mode…\n")
    else:
        sys.stderr.write("🔒 Requesting approval for operation…\n")

    # Ensure we have some message to display
    if not msg.strip():
        if _is_blocked_operation(tool, tool_input):
            msg = f"⚠️  Critical operation requires approval: {tool}"
        else:
            msg = f"Claude requests permission: {tool}"

    # Parse Claude's options if provided
    opts = _parse_options(msg)

    # Enhanced keyboard with persistent permission options
    keyboard, text = _build_keyboard(opts, cmd or msg, tool, tool_input, msg)

    resp = requests.post(
        f"{TG_API}/sendMessage",
        json={
            "chat_id": TG_UID,
            "text": text,
            "parse_mode": "HTML",
            "disable_notification": False,
            "reply_markup": {"inline_keyboard": keyboard},
        },
        timeout=15,
    ).json()
    mid = resp["result"]["message_id"]

    # Persist message id so PostToolUse cleanup can remove keyboard
    try:
        state = {"chat_id": TG_UID, "message_id": mid}
        Path("/tmp/claude_tg_prompt.json").write_text(json.dumps(state))
    except Exception:
        pass

    # ------------------------------------------------------------------
    # Helper functions for button cleanup & annotation
    # ------------------------------------------------------------------

    def _clear_keyboard():
        """Remove inline keyboard so buttons disappear."""
        try:
            requests.post(
                f"{TG_API}/editMessageReplyMarkup",
                json={"chat_id": TG_UID, "message_id": mid, "reply_markup": {}},
                timeout=10,
            )
        except Exception:
            pass

    def _mark_choice(choice_text: str):
        _clear_keyboard()
        try:
            requests.post(
                f"{TG_API}/editMessageText",
                json={
                    "chat_id": TG_UID,
                    "message_id": mid,
                    "text": f"{text}\n\n<b>Selected:</b> {choice_text}",
                    "parse_mode": "HTML",
                },
                timeout=10,
            )
        except Exception:
            pass



    # ------------------------------------------------------------------
    # Wait (max 5 min) for user selection + optional instruction
    # ------------------------------------------------------------------

    offset: int | None = None
    deadline = time.time() + 600  # 10 min
    waiting_for_instr = False

    def _get_updates(offset_: int | None):
        try:
            resp = requests.get(
                f"{TG_API}/getUpdates",
                params={**({"offset": offset_} if offset_ is not None else {}), "timeout": 20},
                timeout=25,
            ).json()
            return resp.get("result", [])
        except Exception:
            return []

    while time.time() < deadline:
        for upd in _get_updates(offset):
            offset = upd.get("update_id", 0) + 1

            # ------------------------------------------------------------------
            # Handle button tap (callback_query)
            # ------------------------------------------------------------------
            if "callback_query" in upd:
                cq = upd["callback_query"]
                data = cq.get("data", "")
                # Ensure it's for our prompt
                if cq.get("message", {}).get("message_id") != mid:
                    continue

                label = opts.get(data, data)

                # Enhanced button handling with support for Claude's numbered options
                sel = data.lower()

                # Handle Claude's numbered options (1, 2, 3, etc.)
                if sel.isdigit():
                    choice_num = sel
                    choice_label = opts.get(sel, f"Option {sel}")
                    _mark_choice(f"✅ Selected: {choice_label}")
                    print(choice_num)
                    sys.exit(0)

                # Handle standard options
                if sel in {"yes", "1"} or label.lower().startswith("yes"):
                    _mark_choice("✅ Yes")
                    print("1")
                    sys.exit(0)

                if sel == "remember" or "remember" in label.lower():
                    # Grant persistent permission
                    cmd_str = tool_input.get("command", "") if tool == "Bash" else ""
                    _grant_persistent_permission(tool, cmd_str, 30)
                    _mark_choice("🔄 Yes, remembered for 30 days")
                    print("1")
                    sys.exit(0)

                if sel in {"session", "2"} or "session" in label.lower():
                    _activate_session()
                    _mark_choice("⏳ Yes in this session")
                    print("1")
                    sys.exit(0)

                if sel in {"no", "4"} or label.lower().startswith("no"):
                    _mark_choice("⛔️ No")
                    print("no")
                    sys.exit(1)

                if sel in {"instruct", "3"} or any(k in label.lower() for k in ("give instruction", "keep planning")):
                    # Ask for follow-up message with enhanced UX
                    waiting_for_instr = True
                    _mark_choice("💬 Waiting for instruction…")
                    requests.post(
                        f"{TG_API}/sendMessage",
                        json={
                            "chat_id": TG_UID,
                            "text": "💬 <b>Please provide your instruction:</b>\n\n"
                                   "You can:\n"
                                   "• Give specific guidance for this operation\n"
                                   "• Provide alternative approach\n"
                                   "• Add additional context or requirements\n\n"
                                   "<i>Send your message as a reply to continue…</i>",
                            "parse_mode": "HTML",
                        },
                        timeout=10,
                    )
                    continue

            # ------------------------------------------------------------------
            # Handle plain text messages when we are waiting for instruction
            # ------------------------------------------------------------------
            if waiting_for_instr and "message" in upd:
                m = upd["message"]
                # Ensure message comes from the same chat
                if str(m.get("chat", {}).get("id")) != TG_UID:
                    continue

                # If it's a reply to another message and not ours, skip.
                rmid = m.get("reply_to_message", {}).get("message_id")
                if rmid and rmid != mid:
                    continue

                instr = m.get("text")
                if not instr:
                    continue  # ignore stickers etc.

                # Enhanced instruction handling
                _mark_choice("✅ Instruction received")

                # Send confirmation with the instruction
                try:
                    requests.post(
                        f"{TG_API}/sendMessage",
                        json={
                            "chat_id": TG_UID,
                            "text": f"✅ <b>Instruction forwarded to Claude Code:</b>\n\n"
                                   f"<i>{instr}</i>\n\n"
                                   f"Claude will continue with your guidance.",
                            "parse_mode": "HTML",
                            "disable_notification": True,
                        },
                        timeout=5,
                    )
                except Exception:
                    pass

                print(instr)
                sys.exit(0)

        # Sleep briefly to avoid tight loop
        time.sleep(0.5)

    # Timeout – for autonomous workflow, default to allow for non-blocked operations
    _clear_keyboard()
    if is_blocked:
        # For blocked operations, timeout means denial
        try:
            requests.post(
                f"{TG_API}/sendMessage",
                json={
                    "chat_id": TG_UID,
                    "text": "⏰ <b>Request timed out</b>\n\n"
                           "Critical operation was denied due to no response.\n"
                           "Claude Code will show the normal permission prompt.",
                    "parse_mode": "HTML",
                },
                timeout=5,
            )
        except Exception:
            pass
        print("no")
        sys.exit(1)
    else:
        # For non-blocked operations, timeout means allow (autonomous mode)
        try:
            requests.post(
                f"{TG_API}/sendMessage",
                json={
                    "chat_id": TG_UID,
                    "text": "⏰ <b>Request timed out - Auto-approved</b>\n\n"
                           "Operation was automatically approved for autonomous workflow.",
                    "parse_mode": "HTML",
                    "disable_notification": True,
                },
                timeout=5,
            )
        except Exception:
            pass
        print("yes")
        sys.exit(0)


if __name__ == "__main__":
    main()
