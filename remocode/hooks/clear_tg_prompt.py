#!/usr/bin/env python3
"""PostToolUse hook – remove inline keyboard from the last Telegram prompt."""
import json, os, sys, requests, pathlib
state_path = pathlib.Path('/tmp/claude_tg_prompt.json')
if not state_path.exists():
    sys.exit(0)
try:
    with state_path.open() as f:
        state = json.load(f)
    chat_id = state['chat_id']
    mid = state['message_id']
    token = os.environ.get('TG_TOKEN')
    if token:
        requests.post(
            f'https://api.telegram.org/bot{token}/editMessageReplyMarkup',
            json={'chat_id': chat_id, 'message_id': mid, 'reply_markup': {}},
            timeout=10,
        )
except Exception:
    pass
finally:
    try:
        state_path.unlink()
    except Exception:
        pass